import { Divider, Icon } from 'ant-design-vue';
import { defineComponent } from 'vue';
import { RouterLink, RouterView } from 'vue-router';

import AppHeader from '@/components/common/app-header';

import styles from './workbench.layout.module.less';

const WorkbenchLayout = defineComponent({
  name: 'WorkbenchLayout',
  render() {
    return (
      <div class="flex min-h-full min-w-full flex-col">
        <AppHeader class="z-1" />
        <div class={[styles.container, 'flex-1']}>
          <aside class={styles.aside}>
            <nav class={styles.nav}>
              <div>
                <RouterLink class={styles.item} to="/guide">
                  <Icon type="book" />
                  <span>项目介绍</span>
                </RouterLink>
              </div>

              <Divider style={{ margin: '10px 0' }} />

              <div>
                <div class={styles.heading}>数据统计</div>
                {/* <div>
                <RouterLink class={styles.item} to="/dashboard">
                  <Icon type="appstore" />
                  <span>仪表盘</span>
                </RouterLink>
              </div> */}

                <div>
                  <RouterLink class={styles.item} to="/defect-rate">
                    <Icon type="solution" />
                    <span>缺陷引入率</span>
                  </RouterLink>
                </div>

                <div>
                  <RouterLink class={styles.item} to="/defect-density">
                    <Icon type="flag" />
                    <span>缺陷密度</span>
                  </RouterLink>
                </div>
                <div>
                  <RouterLink class={styles.item} to="/requirement-defect-rate">
                    <Icon type="flag" />
                    <span>需求缺陷率</span>
                  </RouterLink>
                </div>
                <div>
                  <RouterLink class={styles.item} to="/debt-clean-rate">
                    <Icon type="bulb" />
                    <span>技术债清理率</span>
                  </RouterLink>
                </div>
                {/* <div>
                <RouterLink class={styles.item} to="/team">
                  <Icon type="team" />
                  <span>单元测试覆盖率</span>
                </RouterLink>
              </div> */}
                <div>
                  <RouterLink class={styles.item} to="/unittests">
                    <Icon type="code" />
                    <span>单元测试覆盖率</span>
                  </RouterLink>
                </div>
                <div>
                  <RouterLink class={styles.item} to="/incident-rate">
                    <Icon type="bug" />
                    <span>生产事故率</span>
                  </RouterLink>
                </div>
                <div>
                  <RouterLink class={styles.item} to="/knowledge">
                    <Icon type="share-alt" />
                    <span>知识分享</span>
                  </RouterLink>
                </div>
              </div>

              <Divider style={{ margin: '10px 0' }} />

              <div>
                <div class={styles.heading}>数据管理</div>
                <div>
                  <RouterLink class={styles.item} to="/incident">
                    <Icon type="bug" />
                    <span>生产事故</span>
                  </RouterLink>
                </div>
                <div>
                  <RouterLink class={styles.item} to="/tb-data">
                    <Icon type="bug" />
                    <span>TB数据录入</span>
                  </RouterLink>
                </div>
                <div>
                  <RouterLink class={styles.item} to="/code-stats">
                    <Icon type="bug" />
                    <span>代码统计数据录入</span>
                  </RouterLink>
                </div>
                <div>
                  <RouterLink class={styles.item} to="/analyze">
                    <Icon type="bug" />
                    <span>分析数据</span>
                  </RouterLink>
                </div>
                <div>
                  <RouterLink class={styles.item} to="/product-management">
                    <Icon type="project" />
                    <span>产品管理</span>
                  </RouterLink>
                </div>
                <div>
                  <RouterLink class={styles.item} to="/project-management">
                    <Icon type="folder" />
                    <span>项目管理</span>
                  </RouterLink>
                </div>
              </div>
              <div>
                <div class={styles.heading}>发版管理</div>
                <div>
                  <RouterLink class={styles.item} to="/product-release">
                    <Icon type="bug" />
                    <span>产品发版</span>
                  </RouterLink>
                </div>
                <div>
                  <RouterLink class={styles.item} to="/project-release">
                    <Icon type="bug" />
                    <span>项目发版记录</span>
                  </RouterLink>
                </div>
                <div>
                  <RouterLink class={styles.item} to="/gitlab-pipeline">
                    <Icon type="deployment-unit" />
                    <span>Gitlab Pipeline</span>
                  </RouterLink>
                </div>
              </div>
            </nav>
          </aside>
          <main class={styles.main}>
            <RouterView />
          </main>
        </div>
      </div>
    );
  },
});

export default WorkbenchLayout;
