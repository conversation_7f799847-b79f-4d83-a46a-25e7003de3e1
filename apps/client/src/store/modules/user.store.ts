import { defineStore } from 'pinia';
import { userService } from '@/core/services';

interface UserState {
  currentUser: any | null;
  loading: boolean;
  error: string | null;
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    currentUser: null,
    loading: false,
    error: null,
  }),

  getters: {
    isLoggedIn: (state) => !!state.currentUser,
    isAdmin: (state) => state.currentUser?.role === 'ADMIN',
  },

  actions: {
    async login(username: string, password: string) {
      this.loading = true;
      this.error = null;

      try {
        const response = await userService.login({ username, password });
        this.currentUser = response;
        localStorage.setItem('VITE_AUTH_USER', JSON.stringify(response));
        return response;
      } catch (error: any) {
        this.error = error.message || '登录失败';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async logout() {
      this.loading = true;

      try {
        await userService.logout();
        this.currentUser = null;
        localStorage.removeItem('VITE_AUTH_USER');
      } catch (error: any) {
        this.error = error.message || '登出失败';
      } finally {
        this.loading = false;
      }
    },

    async fetchCurrentUser() {
      this.loading = true;

      try {
        const storedUser = localStorage.getItem('VITE_AUTH_USER');
        if (storedUser) {
          this.currentUser = JSON.parse(storedUser);
        } else {
          const response = await userService.getProfile();
          this.currentUser = response;
          localStorage.setItem('VITE_AUTH_USER', JSON.stringify(response));
        }
      } catch (error: any) {
        this.error = error.message || '获取用户信息失败';
        this.currentUser = null;
        localStorage.removeItem('VITE_AUTH_USER');
      } finally {
        this.loading = false;
      }
    },
  },
});
