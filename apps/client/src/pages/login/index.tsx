import { defineComponent, reactive, ref } from 'vue';
import { message } from 'ant-design-vue';
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue';
import { useUserStore } from '@/store/modules/user.store';
import './index.less';

export default defineComponent({
  name: 'LoginPage',
  setup(props, { root }) {
    const userStore = useUserStore();
    const loading = ref(false);

    const loginForm = reactive({
      username: '',
      password: '',
    });

    const handleSubmit = async () => {
      loading.value = true;
      try {
        await userStore.login(loginForm.username, loginForm.password);
        message.success('登录成功');

        // 如果有重定向参数，则跳转到对应页面，否则跳转到首页
        const redirectPath = (root.$route.query.redirect as string) || '/';
        root.$router.push(redirectPath);
      } catch (error) {
        message.error(error.message || '登录失败，请检查用户名和密码');
      } finally {
        loading.value = false;
      }
    };

    return () => (
      <div class="login-container">
        <div class="login-form-wrapper">
          <div class="login-header">
            <h1 class="login-title">模型训练平台</h1>
            <p class="login-subtitle">管理机器学习模型的全生命周期</p>
          </div>
          <a-form model={loginForm} class="login-form" onFinish={handleSubmit}>
            <a-form-item name="username" rules={[{ required: true, message: '请输入用户名' }]}>
              <a-input
                value={loginForm.username}
                onChange={(e) => (loginForm.username = e.target.value)}
                size="large"
                placeholder="用户名"
              >
                <UserOutlined slot="prefix" />
              </a-input>
            </a-form-item>
            <a-form-item name="password" rules={[{ required: true, message: '请输入密码' }]}>
              <a-input-password
                value={loginForm.password}
                onChange={(e) => (loginForm.password = e.target.value)}
                size="large"
                placeholder="密码"
              >
                <LockOutlined slot="prefix" />
              </a-input-password>
            </a-form-item>
            <a-form-item>
              <a-button type="primary" html-type="submit" size="large" loading={loading.value} block>
                登录
              </a-button>
            </a-form-item>
          </a-form>
        </div>
      </div>
    );
  },
});
