<template>
  <a-modal v-model:visible="visible" title="上传数据集" width="700px" :maskClosable="false" @cancel="handleCancel" :footer="null">
    <a-spin :spinning="uploading">
      <a-form :model="formState" :rules="rules" ref="formRef" layout="vertical" @finish="handleSubmit">
        <a-form-item name="name" label="数据集名称">
          <a-input v-model:value="formState.name" placeholder="请输入数据集名称" />
        </a-form-item>

        <a-form-item name="description" label="数据集描述">
          <a-textarea v-model:value="formState.description" placeholder="请输入数据集描述" :rows="3" />
        </a-form-item>

        <a-form-item name="modelType" label="模型类型">
          <a-select v-model:value="formState.modelType" placeholder="请选择模型类型" @change="handleModelTypeChange">
            <a-select-option value="health">科创健康性模型</a-select-option>
            <a-select-option value="risk">内控风险模型</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item name="tags" label="标签">
          <a-select
            v-model:value="formState.tags"
            mode="tags"
            placeholder="请输入标签，按回车确认"
            :token-separators="[',']"
          ></a-select>
        </a-form-item>

        <a-form-item name="file" label="数据文件">
          <a-upload-dragger
            v-model:fileList="fileList"
            name="file"
            :multiple="false"
            :before-upload="beforeUpload"
            @drop="handleDrop"
            :showUploadList="{ showRemoveIcon: true }"
            accept=".csv"
          >
            <p class="ant-upload-drag-icon">
              <inbox-outlined />
            </p>
            <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p class="ant-upload-hint">支持单个文件上传，仅支持 CSV 格式，文件大小不超过 100MB</p>
            <template v-if="formState.modelType && schemaInfo">
              <a-divider />
              <p class="ant-upload-hint">
                <strong>{{ getModelTypeName(formState.modelType) }}数据集要求：</strong>
              </p>
              <p class="ant-upload-hint">必填字段：{{ schemaInfo.requiredFields.join(', ') }}</p>
            </template>
          </a-upload-dragger>
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="uploading">上传数据集</a-button>
            <a-button @click="handleCancel">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive, defineProps, defineEmits, watch } from 'vue';
import { message } from 'ant-design-vue';
import { InboxOutlined } from '@ant-design/icons-vue';
import { useDatasetStore } from '@/store/modules/dataset.store';
import type { UploadProps } from 'ant-design-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:visible', 'success']);

const datasetStore = useDatasetStore();
const formRef = ref();
const uploading = ref(false);
const fileList = ref<any[]>([]);
const schemaInfo = ref<any>(null);

// 表单状态
const formState = reactive({
  name: '',
  description: '',
  modelType: undefined,
  tags: [],
  file: undefined,
});

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入数据集名称', trigger: 'blur' },
    { min: 2, max: 50, message: '数据集名称长度应为 2-50 个字符', trigger: 'blur' },
  ],
  description: [{ max: 500, message: '描述最多 500 个字符', trigger: 'blur' }],
  modelType: [{ required: true, message: '请选择模型类型', trigger: 'change' }],
  file: [{ required: true, message: '请上传数据文件', trigger: 'change' }],
};

// 监听模型类型变化，获取对应的数据模式
watch(
  () => formState.modelType,
  async (newValue) => {
    if (newValue) {
      try {
        schemaInfo.value = await datasetStore.getDatasetSchema(newValue);
      } catch (error) {
        console.error('获取数据模式失败', error);
      }
    } else {
      schemaInfo.value = null;
    }
  }
);

// 上传前验证
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  // 验证文件类型
  const isCSV = file.type === 'text/csv' || file.name.endsWith('.csv');
  if (!isCSV) {
    message.error('只能上传 CSV 文件!');
    return false;
  }

  // 验证文件大小
  const isLt100M = file.size / 1024 / 1024 < 100;
  if (!isLt100M) {
    message.error('文件大小不能超过 100MB!');
    return false;
  }

  // 更新表单状态
  formState.file = file;
  return false; // 阻止自动上传
};

// 处理拖拽上传
const handleDrop = (e: DragEvent) => {
  e.preventDefault();
  const files = e.dataTransfer?.files;
  if (files && files.length > 0) {
    const file = files[0];
    beforeUpload(file as any, fileList.value);
    if (formState.file) {
      fileList.value = [
        {
          uid: '1',
          name: file.name,
          status: 'done',
          size: file.size,
          type: file.type,
          originFileObj: file,
        },
      ];
    }
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formState.file) {
    message.error('请上传数据文件');
    return;
  }

  uploading.value = true;
  try {
    const formData = new FormData();
    formData.append('name', formState.name);
    formData.append('description', formState.description || '');
    formData.append('modelType', formState.modelType);
    formData.append('tags', JSON.stringify(formState.tags));
    formData.append('file', formState.file);

    await datasetStore.createDataset(formData);
    message.success('数据集上传成功');
    resetForm();
    emit('update:visible', false);
    emit('success');
  } catch (error) {
    console.error('上传数据集失败', error);
  } finally {
    uploading.value = false;
  }
};

// 取消上传
const handleCancel = () => {
  resetForm();
  emit('update:visible', false);
};

// 重置表单
const resetForm = () => {
  formState.name = '';
  formState.description = '';
  formState.modelType = undefined;
  formState.tags = [];
  formState.file = undefined;
  fileList.value = [];
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 处理模型类型变化
const handleModelTypeChange = (value) => {
  formState.modelType = value;
};

// 获取模型类型名称
const getModelTypeName = (type: string) => {
  const types = {
    health: '科创健康性模型',
    risk: '内控风险模型',
  };
  return types[type] || type;
};
</script>

<style scoped>
.ant-upload-hint {
  margin-bottom: 8px;
}
</style>
