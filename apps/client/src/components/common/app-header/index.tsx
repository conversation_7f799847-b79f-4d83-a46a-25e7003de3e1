import { defineComponent, ref, computed } from 'vue';
import { Layout, Menu, Dropdown, Ava<PERSON>, Badge, Button } from 'ant-design-vue';
import {
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
} from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store/modules/user.store';

const { Header } = Layout;

export default defineComponent({
  name: 'AppHeader',
  props: {
    collapsed: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['toggle'],
  setup(props, { emit }) {
    const router = useRouter();
    const userStore = useUserStore();
    const notificationCount = ref(0);

    const currentUser = computed(() => userStore.currentUser);

    // 处理菜单点击
    const handleMenuClick = ({ key }: { key: string }) => {
      if (key === 'logout') {
        handleLogout();
      } else if (key === 'profile') {
        router.push('/profile');
      } else if (key === 'settings') {
        router.push('/settings');
      }
    };

    // 处理退出登录
    const handleLogout = async () => {
      try {
        await userStore.logout();
        router.push('/login');
      } catch (error) {
        console.error('Logout failed:', error);
      }
    };

    // 切换侧边栏折叠状态
    const toggleCollapsed = () => {
      emit('toggle');
    };

    return () => (
      <Header class="app-header">
        <div class="header-left">
          <Button
            type="text"
            onClick={toggleCollapsed}
            class="trigger-button"
          >
            {props.collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          </Button>
          <div class="logo">模型训练平台</div>
        </div>
        <div class="header-right">
          <Badge count={notificationCount.value} class="notification-badge">
            <Button type="text" icon={<BellOutlined />} class="notification-button" />
          </Badge>
          <Dropdown
            v-slots={{
              overlay: () => (
                <Menu onClick={handleMenuClick}>
                  <Menu.Item key="profile">
                    <UserOutlined />
                    个人信息
                  </Menu.Item>
                  <Menu.Item key="settings">
                    <SettingOutlined />
                    设置
                  </Menu.Item>
                  <Menu.Divider />
                  <Menu.Item key="logout">
                    <LogoutOutlined />
                    退出登录
                  </Menu.Item>
                </Menu>
              ),
            }}
          >
            <div class="user-dropdown">
              <Avatar icon={<UserOutlined />} />
              <span class="username">{currentUser.value?.username || '用户'}</span>
            </div>
          </Dropdown>
        </div>
      </Header>
    );
  },
});