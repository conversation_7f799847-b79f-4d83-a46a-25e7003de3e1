import md5 from 'crypto-js/md5';

import { BaseService } from './base.service';

export class AuthService extends BaseService {
  // 登录
  async login(username: string, password: string) {
    // 对密码进行 MD5 加密
    const encryptedPassword = md5(password).toString();
    return this.httpClient.post('/api/auth/login', { username, password: encryptedPassword });
  }

  // 登出
  async logout() {
    return this.httpClient.post('/api/auth/logout');
  }

  // 测试登录状态
  async testLogin() {
    return this.httpClient.get('/api/auth/test');
  }
}
