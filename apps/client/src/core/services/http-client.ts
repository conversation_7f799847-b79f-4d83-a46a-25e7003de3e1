import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { message as Message } from 'ant-design-vue';

export interface HttpClientConfig extends AxiosRequestConfig {
  baseURL: string;
  timeout?: number;
}

export class HttpClient {
  private axiosInstance: AxiosInstance;

  constructor(config: HttpClientConfig) {
    this.axiosInstance = axios.create(config);

    // 请求拦截器
    this.axiosInstance.interceptors.request.use(
      (config) => {
        // 可以在这里添加认证信息等
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.axiosInstance.interceptors.response.use(
      (response) => {
        return response.data;
      },
      (error) => {
        // 处理错误响应
        const { response } = error;
        if (response) {
          // 处理不同的错误状态码
          switch (response.status) {
            case 401:
              // 未授权，跳转到登录页
              window.location.href = '/login';
              break;
            case 403:
              Message.error('没有权限执行此操作');
              break;
            case 404:
              Message.error('请求的资源不存在');
              break;
            case 500:
              Message.error('服务器内部错误');
              break;
            default:
              Message.error(response.data?.message || '请求失败');
          }
        } else {
          // 网络错误
          Message.error('网络错误，请检查您的网络连接');
        }
        return Promise.reject(error);
      }
    );
  }

  // GET 请求
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.axiosInstance.get(url, config);
  }

  // POST 请求
  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.axiosInstance.post(url, data, config);
  }

  // PUT 请求
  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.axiosInstance.put(url, data, config);
  }

  // DELETE 请求
  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.axiosInstance.delete(url, config);
  }
}
