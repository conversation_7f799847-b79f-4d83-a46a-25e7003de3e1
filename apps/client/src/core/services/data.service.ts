import type { HttpClient } from '@/utils/http-client';
import type { PaginationResponse } from '@/core/entities';
import type { UnittestEntity } from '@/core/entities/unittest/unittest.entity';
import type { UnittestSearchDto } from '@/core/entities/unittest/unittest-search.dto';

import { BaseService } from './base.service';

class UnittestService extends BaseService {
  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  static readonly SEARCH_UNITTEST = '/api/data/unittest/search';
  static readonly GET_PROJECT_AGGS = '/api/data/unittest/aggs';
  static readonly GET_TARGETS = '/api/data/unittest/targets/search';
  static readonly CREATE_TARGET = '/api/data/unittest/targets';
  static readonly UPDATE_TARGET = '/api/data/unittest/targets/update';
  static readonly SEARCH_PROJECTS = '/api/data/projects/search';

  searchUnittest = (data: UnittestSearchDto) => {
    return this.httpClient.post<PaginationResponse<UnittestEntity[]>>(UnittestService.SEARCH_UNITTEST, data);
  };

  getProjectAggregations = () => {
    return this.httpClient.post<{ data: Array<{ projectName: string; primaryCount: number; totalCount: number }> }>(
      UnittestService.GET_PROJECT_AGGS,
      {}
    );
  };

  searchProjects = (params?: any) => {
    return this.httpClient.post<
      Array<{ id: number; projectName: string; owner: string; url: string; team: string; createDate: string; updateDate: string }>
    >(UnittestService.SEARCH_PROJECTS, params || {});
  };

  searchUnittestTarget = (params?: any) => {
    return this.httpClient.post<{ data: Array<any> }>(UnittestService.GET_TARGETS, params);
  };

  createUnittestTarget = (data: any) => {
    return this.httpClient.post<any>(UnittestService.CREATE_TARGET, data);
  };

  updateUnittestTarget = (data: any) => {
    return this.httpClient.post<any>(UnittestService.UPDATE_TARGET, data);
  };
}

export interface SearchProjectsRequest {
  projectName?: string;
}

export class DataService {
  unittest: UnittestService;
  httpClient: HttpClient;

  constructor(httpClient: HttpClient) {
    this.unittest = new UnittestService(httpClient);
    this.httpClient = httpClient;
  }

  // 项目相关
  async searchProjects(params?: SearchProjectsRequest) {
    return this.httpClient.post('/api/data/projects/search', params || {});
  }

  // 缺陷引入率相关
  defectRate = {
    searchDefectRateTarget: async (params: any) => {
      return this.httpClient.post('/api/data/defect-rate/targets/search', params);
    },
    createDefectRateTarget: async (params: any) => {
      return this.httpClient.post('/api/data/defect-rate/targets', params);
    },
    updateDefectRateTarget: async (params: any) => {
      return this.httpClient.post('/api/data/defect-rate/targets/update', params);
    },
  };

  // 缺陷密度相关
  defectDensity = {
    searchDefectDensityTarget: async (params: any) => {
      return this.httpClient.post('/api/data/defect-density/targets/search', params);
    },
    createDefectDensityTarget: async (params: any) => {
      return this.httpClient.post('/api/data/defect-density/targets', params);
    },
    updateDefectDensityTarget: async (params: any) => {
      return this.httpClient.post('/api/data/defect-density/targets/update', params);
    },
  };

  // 技术债清理率相关
  debtCleanRate = {
    searchDebtCleanRateTarget: async (params: any) => {
      return this.httpClient.post('/api/data/debt-clean-rate/targets/search', params);
    },
    createDebtCleanRateTarget: async (params: any) => {
      return this.httpClient.post('/api/data/debt-clean-rate/targets', params);
    },
    updateDebtCleanRateTarget: async (params: any) => {
      return this.httpClient.post('/api/data/debt-clean-rate/targets/update', params);
    },
  };
}
