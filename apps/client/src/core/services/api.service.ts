import { HttpClient } from './http-client';

/**
 * 基础服务类，所有服务类都应该继承这个类
 */
export class BaseService {
  protected httpClient: HttpClient;

  constructor(httpClient: HttpClient) {
    this.httpClient = httpClient;
  }
}

/**
 * 数据集服务
 */
export class DatasetService extends BaseService {
  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  static readonly SEARCH = '/api/datasets/search';
  static readonly GET_DETAIL = '/api/datasets';
  static readonly CREATE = '/api/datasets/create';
  static readonly UPDATE = '/api/datasets/update';
  static readonly DELETE = '/api/datasets/delete';
  static readonly PREVIEW = '/api/datasets/preview';

  search = (params: any) => {
    return this.httpClient.get(DatasetService.SEARCH, { params });
  };

  getDetail = (id: number) => {
    return this.httpClient.get(`${DatasetService.GET_DETAIL}/${id}`);
  };

  create = (data: any) => {
    return this.httpClient.post(DatasetService.CREATE, data);
  };

  update = (id: number, data: any) => {
    return this.httpClient.put(`${DatasetService.UPDATE}/${id}`, data);
  };

  delete = (id: number) => {
    return this.httpClient.delete(`${DatasetService.DELETE}/${id}`);
  };

  preview = (id: number) => {
    return this.httpClient.get(`${DatasetService.PREVIEW}/${id}`);
  };
}

/**
 * 训练任务服务
 */
export class TrainingService extends BaseService {
  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  static readonly SEARCH = '/api/training/search';
  static readonly GET_DETAIL = '/api/training';
  static readonly CREATE = '/api/training/create';
  static readonly UPDATE = '/api/training/update';
  static readonly DELETE = '/api/training/delete';
  static readonly START = '/api/training/start';
  static readonly STOP = '/api/training/stop';
  static readonly METRICS = '/api/training/metrics';

  search = (params: any) => {
    return this.httpClient.get(TrainingService.SEARCH, { params });
  };

  getDetail = (id: number) => {
    return this.httpClient.get(`${TrainingService.GET_DETAIL}/${id}`);
  };

  create = (data: any) => {
    return this.httpClient.post(TrainingService.CREATE, data);
  };

  update = (id: number, data: any) => {
    return this.httpClient.put(`${TrainingService.UPDATE}/${id}`, data);
  };

  delete = (id: number) => {
    return this.httpClient.delete(`${TrainingService.DELETE}/${id}`);
  };

  start = (id: number) => {
    return this.httpClient.post(`${TrainingService.START}/${id}`);
  };

  stop = (id: number) => {
    return this.httpClient.post(`${TrainingService.STOP}/${id}`);
  };

  getMetrics = (id: number) => {
    return this.httpClient.get(`${TrainingService.METRICS}/${id}`);
  };
}

/**
 * 模型服务
 */
export class ModelService extends BaseService {
  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  static readonly SEARCH = '/api/models/search';
  static readonly GET_DETAIL = '/api/models';
  static readonly DELETE = '/api/models/delete';
  static readonly EVALUATE = '/api/models/evaluate';
  static readonly COMPARE = '/api/models/compare';

  search = (params: any) => {
    return this.httpClient.get(ModelService.SEARCH, { params });
  };

  getDetail = (id: number) => {
    return this.httpClient.get(`${ModelService.GET_DETAIL}/${id}`);
  };

  delete = (id: number) => {
    return this.httpClient.delete(`${ModelService.DELETE}/${id}`);
  };

  evaluate = (id: number, data: any) => {
    return this.httpClient.post(`${ModelService.EVALUATE}/${id}`, data);
  };

  compare = (ids: number[]) => {
    return this.httpClient.post(ModelService.COMPARE, { ids });
  };
}

/**
 * 部署服务
 */
export class DeploymentService extends BaseService {
  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  static readonly SEARCH = '/api/deployments/search';
  static readonly GET_DETAIL = '/api/deployments';
  static readonly CREATE = '/api/deployments/create';
  static readonly DELETE = '/api/deployments/delete';
  static readonly ROLLBACK = '/api/deployments/rollback';
  static readonly LOGS = '/api/deployments/logs';

  search = (params: any) => {
    return this.httpClient.get(DeploymentService.SEARCH, { params });
  };

  getDetail = (id: number) => {
    return this.httpClient.get(`${DeploymentService.GET_DETAIL}/${id}`);
  };

  create = (data: any) => {
    return this.httpClient.post(DeploymentService.CREATE, data);
  };

  delete = (id: number) => {
    return this.httpClient.delete(`${DeploymentService.DELETE}/${id}`);
  };

  rollback = (id: number, version: number) => {
    return this.httpClient.post(`${DeploymentService.ROLLBACK}/${id}`, { version });
  };

  getLogs = (id: number, params: any) => {
    return this.httpClient.get(`${DeploymentService.LOGS}/${id}`, { params });
  };
}

/**
 * 用户服务
 */
export class UserService extends BaseService {
  constructor(httpClient: HttpClient) {
    super(httpClient);
  }

  static readonly LOGIN = '/api/auth/login';
  static readonly LOGOUT = '/api/auth/logout';
  static readonly GET_PROFILE = '/api/auth/profile';
  static readonly SEARCH = '/api/users/search';
  static readonly UPDATE_PERMISSIONS = '/api/users/permissions';

  login = (data: { username: string; password: string }) => {
    return this.httpClient.post(UserService.LOGIN, data);
  };

  logout = () => {
    return this.httpClient.post(UserService.LOGOUT);
  };

  getProfile = () => {
    return this.httpClient.get(UserService.GET_PROFILE);
  };

  search = (params: any) => {
    return this.httpClient.get(UserService.SEARCH, { params });
  };

  updatePermissions = (id: number, permissions: string[]) => {
    return this.httpClient.put(`${UserService.UPDATE_PERMISSIONS}/${id}`, { permissions });
  };
}
