import { fileURLToPath, URL } from 'node:url';
import { defineConfig } from 'vite';
import legacy from '@vitejs/plugin-legacy';
import vue2 from '@vitejs/plugin-vue2';
import jsx from '@vitejs/plugin-vue2-jsx';
import UnoCSS from 'unocss/vite';
import markdown from 'vite-plugin-md';
import markdownAnchor from 'markdown-it-anchor';
import markdownPrisma from 'markdown-it-prism';

const plugins = [
  vue2({
    include: [/\.vue$/, /\.md$/],
  }),
  jsx(),
  markdown({
    markdownItSetup(md) {
      md.use(markdownAnchor);
      md.use(markdownPrisma);
    },
  }),
  legacy({
    targets: ['chrome >= 87', 'edge >= 88', 'firefox >= 78', 'safari >= 14'],
  }),
  UnoCSS(),
];

const alias = {
  '@': fileURLToPath(new URL('./src', import.meta.url)),
};

export default defineConfig({
  base: '/',
  plugins,
  resolve: {
    alias: alias,
  },
  optimizeDeps: {
    exclude: ['vue-demi'],
    force: true,
  },
  build: {
    commonjsOptions: {
      transformMixedEsModules: true,
    },
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
        math: 'always',
      },
    },
  },
  server: {
    host: true,
    allowedHosts: ['.greatld.com', '.qcc.com'],
    port: 8080,
    proxy: {
      '/api': {
        // target: 'http://p.test.greatld.com',
        target: 'http://localhost:7001',
        changeOrigin: true,
      },
    },
  },
});
