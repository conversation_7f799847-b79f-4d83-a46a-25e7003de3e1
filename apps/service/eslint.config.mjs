import eslint from '@eslint/js';
import tseslint from '@typescript-eslint/eslint-plugin';
import * as tsParser from '@typescript-eslint/parser';
import prettier from 'eslint-plugin-prettier';
import prettierConfig from 'eslint-config-prettier';
import globals from 'globals';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const tsConfig = {
  files: ['**/*.{js,mjs,cjs,jsx,ts,tsx}'],
  ignores: ['**/*.d.ts', 'dist/**', '.eslintrc.js'],
  languageOptions: {
    ecmaVersion: 2020,
    sourceType: 'module',
    parser: tsParser,
    parserOptions: {
      projectService: true,
      tsconfigRootDir: __dirname,
    },
    globals: {
      ...globals.node,
      ...globals.jest,
      ...globals.browser,
    },
  },
  plugins: {
    '@typescript-eslint': tseslint,
    prettier: prettier,
  },
  rules: {
    ...tseslint.configs['recommended'].rules,
    ...prettier.configs.recommended.rules,
    'no-unused-vars': 'off',
    'no-undef': 'off',
    '@typescript-eslint/interface-name-prefix': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/no-unused-vars': 'warn',
    '@typescript-eslint/no-floating-promises': 'warn',
    '@typescript-eslint/no-unsafe-argument': 'warn',
    '@typescript-eslint/no-namespace': 'warn',
    '@typescript-eslint/no-use-before-define': 'warn',
    '@typescript-eslint/no-empty-function': 'warn',
    '@typescript-eslint/ban-ts-comment': 'warn',
    '@typescript-eslint/no-unsafe-call': 'off',
    '@typescript-eslint/no-unsafe-member-access': 'off',
    '@typescript-eslint/no-unsafe-assignment': 'off',
    '@typescript-eslint/no-unsafe-return': 'off',
  },
};

export default [eslint.configs.recommended, prettierConfig, tsConfig, vueConfig];
