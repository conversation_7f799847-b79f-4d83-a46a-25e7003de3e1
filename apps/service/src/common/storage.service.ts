import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { S3Client, GetObjectCommand, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { Readable } from 'stream';

@Injectable()
export class StorageService {
  private s3Client: S3Client;
  private bucket: string;

  constructor(private configService: ConfigService) {
    const rustfsConfig = this.configService.get('storage.rustfs');

    this.s3Client = new S3Client({
      endpoint: rustfsConfig.endpoint,
      region: rustfsConfig.region,
      credentials: {
        accessKeyId: rustfsConfig.accessKey,
        secretAccessKey: rustfsConfig.secretKey,
      },
      forcePathStyle: true, // 通常RUSTFS需要这个设置
    });

    this.bucket = rustfsConfig.bucket;
  }

  /**
   * 上传文件到RUSTFS
   */
  async uploadFile(key: string, body: Buffer | Readable, contentType?: string): Promise<void> {
    const command = new PutObjectCommand({
      Bucket: this.bucket,
      Key: key,
      Body: body,
      ContentType: contentType,
    });

    await this.s3Client.send(command);
  }

  /**
   * 从RUSTFS下载文件
   */
  async downloadFile(key: string): Promise<Readable> {
    const command = new GetObjectCommand({
      Bucket: this.bucket,
      Key: key,
    });

    const response = await this.s3Client.send(command);
    return response.Body as Readable;
  }

  /**
   * 删除RUSTFS中的文件
   */
  async deleteFile(key: string): Promise<void> {
    const command = new DeleteObjectCommand({
      Bucket: this.bucket,
      Key: key,
    });

    await this.s3Client.send(command);
  }

  /**
   * 生成文件存储路径
   */
  generateFilePath(modelType: string, fileName: string): string {
    const timestamp = Date.now();
    return `models/${modelType}/${timestamp}/${fileName}`;
  }

  /**
   * 生成数据集存储路径
   */
  generateDatasetPath(modelType: string, fileName: string): string {
    const timestamp = Date.now();
    return `datasets/${modelType}/${timestamp}/${fileName}`;
  }
}
