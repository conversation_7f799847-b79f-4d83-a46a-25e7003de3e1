import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsObject, IsOptional, IsString, MaxLength } from 'class-validator';
import { ModelStatus } from '../../entities/ModelEntity';

export class UpdateModelDto {
  @ApiProperty({ description: '模型名称', required: false })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  name?: string;

  @ApiProperty({ description: '模型描述', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: '模型状态',
    enum: ModelStatus,
    required: false,
  })
  @IsEnum(ModelStatus)
  @IsOptional()
  status?: ModelStatus;

  @ApiProperty({
    description: '模型指标',
    type: 'object',
    required: false,
    example: {
      accuracy: 0.95,
      precision: 0.92,
      recall: 0.91,
      f1Score: 0.915,
    },
  })
  @IsObject()
  @IsOptional()
  metrics?: Record<string, number>;

  @ApiProperty({
    description: '模型参数',
    type: 'object',
    required: false,
    example: {
      epochs: 100,
      batchSize: 32,
      learningRate: 0.001,
      optimizer: 'adam',
    },
  })
  @IsObject()
  @IsOptional()
  parameters?: Record<string, any>;
}
