import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsObject, IsOptional, IsString, IsUUID, MaxLength } from 'class-validator';
import { ModelType } from '../../datasets/dto/create-dataset.dto';

export class RegisterModelDto {
  @ApiProperty({ description: '模型名称' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({ description: '模型描述', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: '模型类型', enum: ModelType })
  @IsString()
  @IsNotEmpty()
  modelType: ModelType;

  @ApiProperty({ description: '模型版本' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  version: string;

  @ApiProperty({ description: '训练任务ID' })
  @IsUUID()
  @IsNotEmpty()
  trainingJobId: string;

  @ApiProperty({ description: '数据集ID' })
  @IsUUID()
  @IsNotEmpty()
  datasetId: string;

  @ApiProperty({
    description: '模型指标',
    type: 'object',
    example: {
      accuracy: 0.95,
      precision: 0.92,
      recall: 0.91,
      f1Score: 0.915,
    },
    required: false,
  })
  @IsObject()
  @IsOptional()
  metrics?: Record<string, number>;

  @ApiProperty({
    description: '模型参数',
    type: 'object',
    example: {
      epochs: 100,
      batchSize: 32,
      learningRate: 0.001,
      optimizer: 'adam',
    },
    required: false,
  })
  @IsObject()
  @IsOptional()
  parameters?: Record<string, any>;
}
