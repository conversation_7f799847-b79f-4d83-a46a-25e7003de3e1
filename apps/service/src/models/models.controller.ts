import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Res, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { ModelsService, ExportFormat } from './models.service';
import { ModelFiltersDto } from './dto/model-filters.dto';
import { UpdateModelDto } from './dto/update-model.dto';
import { ApproveModelDto } from './dto/approve-model.dto';
import { CompareModelsDto } from './dto/compare-models.dto';
import { RegisterModelDto } from './dto/register-model.dto';
import { ModelEntity } from '../entities';
import { SessionGuard } from '../auth/guards/session.guard';
import { User } from '../auth/decorators/user.decorator';

@ApiTags('模型管理')
@Controller('models')
@UseGuards(SessionGuard)
@ApiBearerAuth()
export class ModelsController {
  constructor(private readonly modelsService: ModelsService) {}

  @Get()
  @ApiOperation({ summary: '获取模型列表' })
  @ApiResponse({ status: 200, description: '成功获取模型列表' })
  async findAll(@Query() filters: ModelFiltersDto): Promise<{ data: ModelEntity[]; total: number }> {
    const [models, total] = await this.modelsService.findAll(filters);
    return { data: models, total };
  }

  @Get(':id')
  @ApiOperation({ summary: '获取单个模型详情' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiResponse({ status: 200, description: '成功获取模型详情' })
  @ApiResponse({ status: 404, description: '模型不存在' })
  async findOne(@Param('id') id: string): Promise<ModelEntity> {
    return this.modelsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新模型信息' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiResponse({ status: 200, description: '成功更新模型信息' })
  @ApiResponse({ status: 404, description: '模型不存在' })
  async update(@Param('id') id: string, @Body() updateModelDto: UpdateModelDto): Promise<ModelEntity> {
    return this.modelsService.update(id, updateModelDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除模型' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiResponse({ status: 200, description: '成功删除模型' })
  @ApiResponse({ status: 400, description: '无法删除已批准的模型' })
  @ApiResponse({ status: 404, description: '模型不存在' })
  async remove(@Param('id') id: string): Promise<{ success: boolean }> {
    await this.modelsService.remove(id);
    return { success: true };
  }

  @Post(':id/approve')
  @ApiOperation({ summary: '批准模型' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiResponse({ status: 200, description: '成功批准模型' })
  @ApiResponse({ status: 400, description: '只有READY状态的模型可以被批准' })
  @ApiResponse({ status: 404, description: '模型不存在' })
  async approveModel(
    @Param('id') id: string,
    @Body() approveModelDto: ApproveModelDto,
    @User('id') userId: string
  ): Promise<ModelEntity> {
    return this.modelsService.approveModel(id, approveModelDto, userId);
  }

  @Post('compare')
  @ApiOperation({ summary: '比较多个模型' })
  @ApiResponse({ status: 200, description: '成功比较模型' })
  @ApiResponse({ status: 400, description: '无法比较不同类型的模型' })
  async compareModels(@Body() compareModelsDto: CompareModelsDto): Promise<any> {
    return this.modelsService.compareModels(compareModelsDto);
  }

  @Get(':id/download')
  @ApiOperation({ summary: '下载模型' })
  @ApiParam({ name: 'id', description: '模型ID' })
  @ApiQuery({ name: 'format', enum: ExportFormat, required: false, description: '导出格式' })
  @ApiResponse({ status: 200, description: '成功下载模型' })
  @ApiResponse({ status: 404, description: '模型不存在' })
  async downloadModel(@Param('id') id: string, @Query('format') format: ExportFormat, @Res({ passthrough: true }) res: Response) {
    const file = await this.modelsService.downloadModel(id, format);

    // 设置响应头
    const fileName = `model-${id}.${format || ExportFormat.ZIP}`;
    res.set({
      'Content-Disposition': `attachment; filename="${fileName}"`,
      'Content-Type': format === ExportFormat.JSON ? 'application/json' : 'application/zip',
    });

    return file;
  }

  @Post('register')
  @ApiOperation({ summary: '注册模型' })
  @ApiResponse({ status: 201, description: '成功注册模型' })
  @ApiResponse({ status: 400, description: '模型类型不匹配或版本格式错误' })
  @ApiResponse({ status: 404, description: '训练任务或数据集不存在' })
  async registerModel(@Body() registerModelDto: RegisterModelDto, @User('id') userId: string): Promise<ModelEntity> {
    return this.modelsService.registerModel(registerModelDto, userId);
  }

  @Get('metrics/:modelType')
  @ApiOperation({ summary: '获取模型类型特定的指标定义' })
  @ApiParam({ name: 'modelType', description: '模型类型' })
  @ApiResponse({ status: 200, description: '成功获取指标定义' })
  @ApiResponse({ status: 400, description: '不支持的模型类型' })
  async getModelTypeMetrics(@Param('modelType') modelType: string): Promise<any> {
    return this.modelsService.getModelTypeMetrics(modelType);
  }
}
