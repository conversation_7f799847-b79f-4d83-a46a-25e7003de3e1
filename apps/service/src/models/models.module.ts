import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ModelEntity, TrainingJobEntity, DatasetEntity } from '../entities';
import { ModelsService } from './models.service';
import { ModelsController } from './models.controller';
import { CommonModule } from '../common/common.module';
import { DatasetsModule } from '../datasets/datasets.module';

@Module({
  imports: [TypeOrmModule.forFeature([ModelEntity, TrainingJobEntity, DatasetEntity]), CommonModule, DatasetsModule],
  controllers: [ModelsController],
  providers: [ModelsService],
  exports: [ModelsService],
})
export class ModelsModule {}
