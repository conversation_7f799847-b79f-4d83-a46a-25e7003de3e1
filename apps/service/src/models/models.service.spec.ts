import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ModelsService, ExportFormat } from './models.service';
import { ModelEntity, ModelStatus, TrainingJobEntity, DatasetEntity } from '../entities';
import { StorageService } from '../common/storage.service';
import { BadRequestException, NotFoundException, StreamableFile } from '@nestjs/common';
import { RegisterModelDto } from './dto/register-model.dto';
import { UpdateModelDto } from './dto/update-model.dto';
import { ApproveModelDto } from './dto/approve-model.dto';
import { CompareModelsDto } from './dto/compare-models.dto';
import { Readable } from 'stream';

describe('ModelsService', () => {
  let service: ModelsService;
  let modelRepository: Repository<ModelEntity>;
  let trainingJobRepository: Repository<TrainingJobEntity>;
  let datasetRepository: Repository<DatasetEntity>;
  let storageService: StorageService;

  const mockModelRepository = {
    createQueryBuilder: jest.fn(() => ({
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      getManyAndCount: jest.fn().mockResolvedValue([[mockModel], 1]),
    })),
    findOne: jest.fn(),
    save: jest.fn(),
    remove: jest.fn(),
  };

  const mockTrainingJobRepository = {
    findOne: jest.fn(),
  };

  const mockDatasetRepository = {
    findOne: jest.fn(),
  };

  const mockStorageService = {
    deleteFile: jest.fn(),
    downloadFile: jest.fn(),
    generateFilePath: jest.fn(),
  };

  const mockModel = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    name: '测试模型',
    description: '这是一个测试模型',
    modelType: 'health_model',
    version: '1.0.0',
    trainingJobId: '123e4567-e89b-12d3-a456-426614174001',
    datasetId: '123e4567-e89b-12d3-a456-426614174002',
    metrics: { accuracy: 0.95 },
    parameters: { epochs: 100 },
    filePath: 'models/health_model/**********/model-1.0.0.bin',
    createdAt: new Date(),
    createdBy: 'user123',
    status: ModelStatus.READY,
    approvedBy: null,
    approvedAt: null,
  };

  const mockTrainingJob = {
    id: '123e4567-e89b-12d3-a456-426614174001',
    modelType: 'health_model',
    modelFilePath: 'models/health_model/**********/model-1.0.0.bin',
  };

  const mockDataset = {
    id: '123e4567-e89b-12d3-a456-426614174002',
    modelType: 'health_model',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ModelsService,
        {
          provide: getRepositoryToken(ModelEntity),
          useValue: mockModelRepository,
        },
        {
          provide: getRepositoryToken(TrainingJobEntity),
          useValue: mockTrainingJobRepository,
        },
        {
          provide: getRepositoryToken(DatasetEntity),
          useValue: mockDatasetRepository,
        },
        {
          provide: StorageService,
          useValue: mockStorageService,
        },
      ],
    }).compile();

    service = module.get<ModelsService>(ModelsService);
    modelRepository = module.get<Repository<ModelEntity>>(getRepositoryToken(ModelEntity));
    trainingJobRepository = module.get<Repository<TrainingJobEntity>>(getRepositoryToken(TrainingJobEntity));
    datasetRepository = module.get<Repository<DatasetEntity>>(getRepositoryToken(DatasetEntity));
    storageService = module.get<StorageService>(StorageService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('should return an array of models and count', async () => {
      const result = await service.findAll({});
      expect(result).toEqual([[mockModel], 1]);
      expect(mockModelRepository.createQueryBuilder).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a model', async () => {
      mockModelRepository.findOne.mockResolvedValue(mockModel);
      const result = await service.findOne('123e4567-e89b-12d3-a456-426614174000');
      expect(result).toEqual(mockModel);
      expect(mockModelRepository.findOne).toHaveBeenCalledWith({
        where: { id: '123e4567-e89b-12d3-a456-426614174000' },
        relations: ['trainingJob', 'dataset'],
      });
    });

    it('should throw NotFoundException if model not found', async () => {
      mockModelRepository.findOne.mockResolvedValue(null);
      await expect(service.findOne('not-exist')).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should update and return a model', async () => {
      const updateDto: UpdateModelDto = {
        name: '更新后的模型名称',
        description: '更新后的描述',
      };
      mockModelRepository.findOne.mockResolvedValue(mockModel);
      mockModelRepository.save.mockResolvedValue({ ...mockModel, ...updateDto });

      const result = await service.update('123e4567-e89b-12d3-a456-426614174000', updateDto);
      expect(result).toEqual({ ...mockModel, ...updateDto });
      expect(mockModelRepository.save).toHaveBeenCalled();
    });
  });

  describe('remove', () => {
    it('should remove a model', async () => {
      mockModelRepository.findOne.mockResolvedValue(mockModel);
      await service.remove('123e4567-e89b-12d3-a456-426614174000');
      expect(mockStorageService.deleteFile).toHaveBeenCalledWith(mockModel.filePath);
      expect(mockModelRepository.remove).toHaveBeenCalledWith(mockModel);
    });

    it('should throw BadRequestException if model is approved', async () => {
      mockModelRepository.findOne.mockResolvedValue({ ...mockModel, status: ModelStatus.APPROVED });
      await expect(service.remove('123e4567-e89b-12d3-a456-426614174000')).rejects.toThrow(BadRequestException);
    });
  });

  describe('approveModel', () => {
    it('should approve a model', async () => {
      const approveDto: ApproveModelDto = { comment: '批准通过' };
      mockModelRepository.findOne.mockResolvedValue(mockModel);
      mockModelRepository.save.mockImplementation((model) => Promise.resolve(model));

      const result = await service.approveModel('123e4567-e89b-12d3-a456-426614174000', approveDto, 'admin123');
      expect(result.status).toBe(ModelStatus.APPROVED);
      expect(result.approvedBy).toBe('admin123');
      expect(result.approvedAt).toBeInstanceOf(Date);
      expect(mockModelRepository.save).toHaveBeenCalled();
    });

    it('should throw BadRequestException if model is not in READY status', async () => {
      mockModelRepository.findOne.mockResolvedValue({ ...mockModel, status: ModelStatus.TRAINING });
      await expect(
        service.approveModel('123e4567-e89b-12d3-a456-426614174000', { comment: '批准通过' }, 'admin123')
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('compareModels', () => {
    it('should compare models of the same type', async () => {
      const model1 = { ...mockModel, id: '123e4567-e89b-12d3-a456-426614174000' };
      const model2 = { ...mockModel, id: '123e4567-e89b-12d3-a456-426614174001', metrics: { accuracy: 0.97 } };
      mockModelRepository.findOne.mockImplementation((options) => {
        if (options.where.id === '123e4567-e89b-12d3-a456-426614174000') {
          return Promise.resolve(model1);
        } else {
          return Promise.resolve(model2);
        }
      });

      const compareDto: CompareModelsDto = {
        modelIds: ['123e4567-e89b-12d3-a456-426614174000', '123e4567-e89b-12d3-a456-426614174001'],
      };

      const result = await service.compareModels(compareDto);
      expect(result.modelType).toBe('health_model');
      expect(result.models).toHaveLength(2);
      expect(result.metricNames).toContain('accuracy');
    });

    it('should throw BadRequestException if models are of different types', async () => {
      const model1 = { ...mockModel, id: '123e4567-e89b-12d3-a456-426614174000', modelType: 'health_model' };
      const model2 = { ...mockModel, id: '123e4567-e89b-12d3-a456-426614174001', modelType: 'risk_model' };
      mockModelRepository.findOne.mockImplementation((options) => {
        if (options.where.id === '123e4567-e89b-12d3-a456-426614174000') {
          return Promise.resolve(model1);
        } else {
          return Promise.resolve(model2);
        }
      });

      const compareDto: CompareModelsDto = {
        modelIds: ['123e4567-e89b-12d3-a456-426614174000', '123e4567-e89b-12d3-a456-426614174001'],
      };

      await expect(service.compareModels(compareDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('registerModel', () => {
    it('should register a new model', async () => {
      const registerDto: RegisterModelDto = {
        name: '新模型',
        description: '这是一个新模型',
        modelType: 'health_model',
        version: '1.0.0',
        trainingJobId: '123e4567-e89b-12d3-a456-426614174001',
        datasetId: '123e4567-e89b-12d3-a456-426614174002',
        metrics: { accuracy: 0.95 },
        parameters: { epochs: 100 },
      };

      mockTrainingJobRepository.findOne.mockResolvedValue(mockTrainingJob);
      mockDatasetRepository.findOne.mockResolvedValue(mockDataset);
      mockModelRepository.save.mockImplementation((model) => Promise.resolve(model));

      const result = await service.registerModel(registerDto, 'user123');
      expect(result.name).toBe(registerDto.name);
      expect(result.modelType).toBe(registerDto.modelType);
      expect(result.status).toBe(ModelStatus.READY);
      expect(result.createdBy).toBe('user123');
      expect(mockModelRepository.save).toHaveBeenCalled();
    });

    it('should throw NotFoundException if training job not found', async () => {
      mockTrainingJobRepository.findOne.mockResolvedValue(null);

      const registerDto: RegisterModelDto = {
        name: '新模型',
        description: '这是一个新模型',
        modelType: 'health_model',
        version: '1.0.0',
        trainingJobId: 'not-exist',
        datasetId: '123e4567-e89b-12d3-a456-426614174002',
      };

      await expect(service.registerModel(registerDto, 'user123')).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException if model type does not match training job', async () => {
      mockTrainingJobRepository.findOne.mockResolvedValue({ ...mockTrainingJob, modelType: 'risk_model' });
      mockDatasetRepository.findOne.mockResolvedValue(mockDataset);

      const registerDto: RegisterModelDto = {
        name: '新模型',
        description: '这是一个新模型',
        modelType: 'health_model',
        version: '1.0.0',
        trainingJobId: '123e4567-e89b-12d3-a456-426614174001',
        datasetId: '123e4567-e89b-12d3-a456-426614174002',
      };

      await expect(service.registerModel(registerDto, 'user123')).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException if version format is invalid', async () => {
      mockTrainingJobRepository.findOne.mockResolvedValue(mockTrainingJob);
      mockDatasetRepository.findOne.mockResolvedValue(mockDataset);

      const registerDto: RegisterModelDto = {
        name: '新模型',
        description: '这是一个新模型',
        modelType: 'health_model',
        version: 'invalid-version',
        trainingJobId: '123e4567-e89b-12d3-a456-426614174001',
        datasetId: '123e4567-e89b-12d3-a456-426614174002',
      };

      await expect(service.registerModel(registerDto, 'user123')).rejects.toThrow(BadRequestException);
    });
  });

  describe('getModelTypeMetrics', () => {
    it('should return metrics for health model', async () => {
      const result = await service.getModelTypeMetrics('health_model');
      expect(result).toHaveProperty('accuracy');
      expect(result).toHaveProperty('precision');
      expect(result).toHaveProperty('recall');
      expect(result).toHaveProperty('f1Score');
    });

    it('should return metrics for risk model', async () => {
      const result = await service.getModelTypeMetrics('risk_model');
      expect(result).toHaveProperty('auc');
      expect(result).toHaveProperty('ks');
      expect(result).toHaveProperty('gini');
      expect(result).toHaveProperty('psi');
    });

    it('should throw BadRequestException for unsupported model type', async () => {
      await expect(service.getModelTypeMetrics('unsupported_type')).rejects.toThrow(BadRequestException);
    });
  });
});
