import { BadRequestException, Injectable, NotFoundException, StreamableFile } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ModelEntity, ModelStatus, TrainingJobEntity, DatasetEntity } from '../entities';
import { StorageService } from '../common/storage.service';
import { ModelFiltersDto } from './dto/model-filters.dto';
import { UpdateModelDto } from './dto/update-model.dto';
import { ApproveModelDto } from './dto/approve-model.dto';
import { CompareModelsDto } from './dto/compare-models.dto';
import { RegisterModelDto } from './dto/register-model.dto';
import { createReadStream } from 'fs';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import * as archiver from 'archiver';

export enum ExportFormat {
  ZIP = 'zip',
  JSON = 'json',
}

@Injectable()
export class ModelsService {
  constructor(
    @InjectRepository(ModelEntity)
    private modelRepository: Repository<ModelEntity>,
    @InjectRepository(TrainingJobEntity)
    private trainingJobRepository: Repository<TrainingJobEntity>,
    @InjectRepository(DatasetEntity)
    private datasetRepository: Repository<DatasetEntity>,
    private storageService: StorageService
  ) {}

  /**
   * 查找所有模型
   */
  async findAll(filters: ModelFiltersDto): Promise<[ModelEntity[], number]> {
    const query = this.modelRepository.createQueryBuilder('model');

    if (filters.modelType) {
      query.andWhere('model.modelType = :modelType', { modelType: filters.modelType });
    }

    if (filters.status) {
      query.andWhere('model.status = :status', { status: filters.status });
    }

    if (filters.trainingJobId) {
      query.andWhere('model.trainingJobId = :trainingJobId', { trainingJobId: filters.trainingJobId });
    }

    if (filters.datasetId) {
      query.andWhere('model.datasetId = :datasetId', { datasetId: filters.datasetId });
    }

    if (filters.createdBy) {
      query.andWhere('model.createdBy = :createdBy', { createdBy: filters.createdBy });
    }

    if (filters.search) {
      query.andWhere('(model.name LIKE :search OR model.description LIKE :search)', {
        search: `%${filters.search}%`,
      });
    }

    query.orderBy('model.createdAt', filters.sortOrder || 'DESC');

    if (filters.skip !== undefined) {
      query.skip(filters.skip);
    }

    if (filters.take !== undefined) {
      query.take(filters.take);
    }

    return query.getManyAndCount();
  }

  /**
   * 查找单个模型
   */
  async findOne(id: string): Promise<ModelEntity> {
    const model = await this.modelRepository.findOne({
      where: { id },
      relations: ['trainingJob', 'dataset'],
    });

    if (!model) {
      throw new NotFoundException(`Model with ID ${id} not found`);
    }

    return model;
  }

  /**
   * 更新模型
   */
  async update(id: string, updateModelDto: UpdateModelDto): Promise<ModelEntity> {
    const model = await this.findOne(id);

    // 只允许更新某些字段
    if (updateModelDto.name) model.name = updateModelDto.name;
    if (updateModelDto.description) model.description = updateModelDto.description;
    if (updateModelDto.status) model.status = updateModelDto.status;
    if (updateModelDto.metrics) model.metrics = updateModelDto.metrics;
    if (updateModelDto.parameters) model.parameters = updateModelDto.parameters;

    return this.modelRepository.save(model);
  }

  /**
   * 删除模型
   */
  async remove(id: string): Promise<void> {
    const model = await this.findOne(id);

    // 如果模型已经被批准，不允许删除
    if (model.status === ModelStatus.APPROVED) {
      throw new BadRequestException('Cannot delete an approved model');
    }

    // 删除存储中的文件
    try {
      await this.storageService.deleteFile(model.filePath);
    } catch (error) {
      console.error('Error deleting model file:', error);
      // 继续删除数据库记录，即使文件删除失败
    }

    await this.modelRepository.remove(model);
  }

  /**
   * 批准模型
   */
  async approveModel(id: string, approveModelDto: ApproveModelDto, userId: string): Promise<ModelEntity> {
    const model = await this.findOne(id);

    // 只有READY状态的模型可以被批准
    if (model.status !== ModelStatus.READY) {
      throw new BadRequestException(`Only models with status READY can be approved. Current status: ${model.status}`);
    }

    // 更新模型状态
    model.status = ModelStatus.APPROVED;
    model.approvedBy = userId;
    model.approvedAt = new Date();

    // 在实际应用中，这里可能需要记录审批意见

    return this.modelRepository.save(model);
  }

  /**
   * 比较模型
   */
  async compareModels(compareModelsDto: CompareModelsDto): Promise<any> {
    const { modelIds } = compareModelsDto;

    // 获取所有模型
    const models = await Promise.all(modelIds.map((id) => this.findOne(id)));

    // 检查所有模型是否属于同一类型
    const modelTypes = new Set(models.map((model) => model.modelType));
    if (modelTypes.size > 1) {
      throw new BadRequestException('Cannot compare models of different types');
    }

    // 提取所有模型的指标
    const comparisonData = models.map((model) => ({
      id: model.id,
      name: model.name,
      version: model.version,
      createdAt: model.createdAt,
      metrics: model.metrics || {},
      parameters: model.parameters || {},
    }));

    // 获取所有指标的名称
    const allMetricNames = new Set<string>();
    comparisonData.forEach((data) => {
      Object.keys(data.metrics).forEach((key) => allMetricNames.add(key));
    });

    // 构建比较结果
    const comparison = {
      modelType: models[0].modelType,
      models: comparisonData,
      metricNames: Array.from(allMetricNames),
    };

    return comparison;
  }

  /**
   * 下载模型
   */
  async downloadModel(id: string, format: ExportFormat = ExportFormat.ZIP): Promise<StreamableFile> {
    const model = await this.findOne(id);

    // 创建临时文件
    const tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'model-export-'));
    const tempFilePath = path.join(tempDir, `model-${id}.${format}`);

    try {
      if (format === ExportFormat.ZIP) {
        // 创建ZIP文件
        const output = fs.createWriteStream(tempFilePath);
        const archive = archiver('zip', { zlib: { level: 9 } });

        archive.pipe(output);

        // 从存储中下载模型文件
        const modelStream = await this.storageService.downloadFile(model.filePath);
        archive.append(modelStream, { name: 'model.bin' });

        // 添加元数据
        const metadata = {
          id: model.id,
          name: model.name,
          description: model.description,
          modelType: model.modelType,
          version: model.version,
          metrics: model.metrics,
          parameters: model.parameters,
          createdAt: model.createdAt,
        };

        archive.append(JSON.stringify(metadata, null, 2), { name: 'metadata.json' });

        await archive.finalize();

        // 等待写入完成
        await new Promise<void>((resolve) => {
          output.on('close', () => resolve());
        });
      } else if (format === ExportFormat.JSON) {
        // 创建JSON文件
        const metadata = {
          id: model.id,
          name: model.name,
          description: model.description,
          modelType: model.modelType,
          version: model.version,
          metrics: model.metrics,
          parameters: model.parameters,
          createdAt: model.createdAt,
          filePath: model.filePath,
        };

        fs.writeFileSync(tempFilePath, JSON.stringify(metadata, null, 2));
      } else {
        throw new BadRequestException(`Unsupported export format: ${format}`);
      }

      // 创建可流式传输的文件
      const file = createReadStream(tempFilePath);
      return new StreamableFile(file);
    } catch (error) {
      // 清理临时文件
      try {
        fs.unlinkSync(tempFilePath);
        fs.rmdirSync(tempDir);
      } catch (cleanupError) {
        console.error('Error cleaning up temporary files:', cleanupError);
      }

      throw error;
    } finally {
      // 设置定时器清理临时文件
      setTimeout(() => {
        try {
          fs.unlinkSync(tempFilePath);
          fs.rmdirSync(tempDir);
        } catch (cleanupError) {
          console.error('Error cleaning up temporary files:', cleanupError);
        }
      }, 60000); // 1分钟后清理
    }
  }

  /**
   * 注册模型
   */
  async registerModel(registerModelDto: RegisterModelDto, userId: string): Promise<ModelEntity> {
    // 验证训练任务是否存在
    const trainingJob = await this.trainingJobRepository.findOne({
      where: { id: registerModelDto.trainingJobId },
    });

    if (!trainingJob) {
      throw new NotFoundException(`Training job with ID ${registerModelDto.trainingJobId} not found`);
    }

    // 验证数据集是否存在
    const dataset = await this.datasetRepository.findOne({
      where: { id: registerModelDto.datasetId },
    });

    if (!dataset) {
      throw new NotFoundException(`Dataset with ID ${registerModelDto.datasetId} not found`);
    }

    // 验证模型类型是否一致
    if (registerModelDto.modelType !== trainingJob.modelType) {
      throw new BadRequestException(
        `Model type ${registerModelDto.modelType} does not match training job model type ${trainingJob.modelType}`
      );
    }

    // 检查版本号格式是否符合语义化版本规范
    const versionRegex = /^(\d+)\.(\d+)\.(\d+)(?:-([0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*))?(?:\+[0-9A-Za-z-]+)?$/;
    if (!versionRegex.test(registerModelDto.version)) {
      throw new BadRequestException('Version must follow semantic versioning format (e.g., 1.0.0)');
    }

    // 创建模型实体
    const model = new ModelEntity();
    model.name = registerModelDto.name;
    model.description = registerModelDto.description;
    model.modelType = registerModelDto.modelType;
    model.version = registerModelDto.version;
    model.trainingJobId = registerModelDto.trainingJobId;
    model.datasetId = registerModelDto.datasetId;
    model.metrics = registerModelDto.metrics || {};
    model.parameters = registerModelDto.parameters || {};
    model.createdBy = userId;
    model.status = ModelStatus.READY;

    // 生成文件路径（假设模型文件已经由训练任务生成）
    model.filePath =
      trainingJob.modelFilePath || this.storageService.generateFilePath(model.modelType, `model-${model.version}.bin`);

    // 保存模型
    return this.modelRepository.save(model);
  }

  /**
   * 获取模型类型特定的指标定义
   */
  async getModelTypeMetrics(modelType: string): Promise<any> {
    // 返回不同模型类型的指标定义
    if (modelType === 'health_model') {
      return {
        accuracy: {
          name: '准确率',
          description: '模型预测正确的比例',
          range: [0, 1],
          higherIsBetter: true,
        },
        precision: {
          name: '精确率',
          description: '模型预测为正的样本中实际为正的比例',
          range: [0, 1],
          higherIsBetter: true,
        },
        recall: {
          name: '召回率',
          description: '实际为正的样本中被模型预测为正的比例',
          range: [0, 1],
          higherIsBetter: true,
        },
        f1Score: {
          name: 'F1分数',
          description: '精确率和召回率的调和平均值',
          range: [0, 1],
          higherIsBetter: true,
        },
      };
    } else if (modelType === 'risk_model') {
      return {
        auc: {
          name: 'AUC',
          description: 'ROC曲线下面积',
          range: [0, 1],
          higherIsBetter: true,
        },
        ks: {
          name: 'KS值',
          description: 'Kolmogorov-Smirnov统计量',
          range: [0, 1],
          higherIsBetter: true,
        },
        gini: {
          name: 'Gini系数',
          description: '基尼系数，衡量模型区分能力',
          range: [0, 1],
          higherIsBetter: true,
        },
        psi: {
          name: 'PSI',
          description: '群体稳定性指标',
          range: [0, Infinity],
          higherIsBetter: false,
          threshold: 0.1,
        },
      };
    } else {
      throw new BadRequestException(`Unsupported model type: ${modelType}`);
    }
  }
}
