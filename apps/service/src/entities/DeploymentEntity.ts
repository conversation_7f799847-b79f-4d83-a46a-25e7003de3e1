import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ModelEntity } from './ModelEntity';

export enum DeploymentStatus {
  DEPLOYING = 'deploying',
  RUNNING = 'running',
  STOPPED = 'stopped',
  ERROR = 'error',
}

@Entity('deployments')
@Index(['modelId'])
@Index(['modelType'])
@Index(['status'])
@Index(['createdBy'])
export class DeploymentEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @Column({ name: 'model_id', length: 36 })
  modelId: string;

  @ManyToOne(() => ModelEntity)
  @JoinColumn({ name: 'model_id' })
  model: ModelEntity;

  @Column({ name: 'model_type', length: 50 })
  modelType: string;

  @Column({ name: 'engine_id', length: 36 })
  engineId: string;

  @Column({ length: 500 })
  endpoint: string;

  @Column({ length: 50 })
  version: string;

  @Column({
    type: 'enum',
    enum: DeploymentStatus,
    default: DeploymentStatus.DEPLOYING,
  })
  status: DeploymentStatus;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by', length: 36 })
  createdBy: string;

  @Column('json', { nullable: true })
  metrics: Record<string, number>;
}
