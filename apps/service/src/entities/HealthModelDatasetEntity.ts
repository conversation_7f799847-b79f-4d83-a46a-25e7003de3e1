import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn, Index } from 'typeorm';
import { DatasetEntity } from './DatasetEntity';

@Entity('health_model_datasets')
@Index(['datasetId'])
export class HealthModelDatasetEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'dataset_id', length: 36 })
  datasetId: string;

  @ManyToOne(() => DatasetEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'dataset_id' })
  dataset: DatasetEntity;

  // 企业基本信息
  @Column({ name: 'enterprise_id', length: 36, nullable: true })
  enterpriseId: string;

  @Column({ name: 'enterprise_name', length: 255, nullable: true })
  enterpriseName: string;

  // 科创健康性指标
  @Column({ name: 'innovation_health_level', length: 50, nullable: true })
  innovationHealthLevel: string;

  @Column({ name: 'comprehensive_score', type: 'float', nullable: true })
  comprehensiveScore: number;

  @Column({ name: 'label', type: 'int', nullable: true })
  label: number;

  // 技术专利指标
  @Column({ name: 'tech_patent_application_ratio', type: 'float', nullable: true })
  techPatentApplicationRatio: number;

  @Column({ name: 'tech_patent_rejection_rate', type: 'float', nullable: true })
  techPatentRejectionRate: number;

  @Column({ name: 'tech_patent_authorization_rate', type: 'float', nullable: true })
  techPatentAuthorizationRate: number;

  @Column({ name: 'tech_patent_maintenance_rate', type: 'float', nullable: true })
  techPatentMaintenanceRate: number;

  @Column({ name: 'tech_patent_authorization_stability', type: 'float', nullable: true })
  techPatentAuthorizationStability: number;

  @Column({ name: 'tech_patent_authorization_ranking', type: 'float', nullable: true })
  techPatentAuthorizationRanking: number;

  @Column({ name: 'tech_software_copyright_ranking', type: 'float', nullable: true })
  techSoftwareCopyrightRanking: number;

  @Column({ name: 'tech_patent_concentration', type: 'float', nullable: true })
  techPatentConcentration: number;

  @Column({ name: 'tech_external_patent_ratio', type: 'float', nullable: true })
  techExternalPatentRatio: number;

  @Column({ name: 'tech_patent_continuity', type: 'int', nullable: true })
  techPatentContinuity: number;

  @Column({ name: 'tech_adj_patent_outflow', type: 'int', nullable: true })
  techAdjPatentOutflow: number;

  @Column({ name: 'tech_adj_pct_patent', type: 'int', nullable: true })
  techAdjPctPatent: number;

  @Column({ name: 'tech_adj_ip_pledge', type: 'int', nullable: true })
  techAdjIpPledge: number;

  @Column({ name: 'tech_adj_ip_transformation', type: 'float', nullable: true })
  techAdjIpTransformation: number;

  @Column({ name: 'tech_adj_tech_achievement', type: 'int', nullable: true })
  techAdjTechAchievement: number;

  // 发展指标
  @Column({ name: 'dev_talent_stability', type: 'float', nullable: true })
  devTalentStability: number;

  @Column({ name: 'dev_equity_financing', type: 'int', nullable: true })
  devEquityFinancing: number;

  @Column({ name: 'dev_enterprise_honor', type: 'int', nullable: true })
  devEnterpriseHonor: number;

  @Column({ name: 'dev_adj_employee_shareholding', type: 'int', nullable: true })
  devAdjEmployeeShareholding: number;

  @Column({ name: 'dev_adj_honor_cancellation', type: 'int', nullable: true })
  devAdjHonorCancellation: number;

  // 运营指标
  @Column({ name: 'oper_capital_paid_ratio', type: 'float', nullable: true })
  operCapitalPaidRatio: number;

  @Column({ name: 'oper_key_personnel_change', type: 'int', nullable: true })
  operKeyPersonnelChange: number;

  @Column({ name: 'oper_equity_change_frequency', type: 'int', nullable: true })
  operEquityChangeFrequency: number;

  @Column({ name: 'oper_adj_capital_reduction', type: 'int', nullable: true })
  operAdjCapitalReduction: number;

  @Column({ name: 'oper_adj_equity_structure', type: 'int', nullable: true })
  operAdjEquityStructure: number;

  @Column({ name: 'oper_adj_related_party_change', type: 'float', nullable: true })
  operAdjRelatedPartyChange: number;

  @Column({ name: 'oper_adj_business_status', type: 'int', nullable: true })
  operAdjBusinessStatus: number;

  @Column({ name: 'oper_adj_revocation', type: 'int', nullable: true })
  operAdjRevocation: number;

  // 风险指标
  @Column({ name: 'risk_adj_execution_restriction', type: 'int', nullable: true })
  riskAdjExecutionRestriction: number;

  @Column({ name: 'risk_adj_financial_litigation', type: 'int', nullable: true })
  riskAdjFinancialLitigation: number;

  @Column({ name: 'risk_adj_environmental_penalty', type: 'int', nullable: true })
  riskAdjEnvironmentalPenalty: number;

  @Column({ name: 'risk_adj_tax_arrears', type: 'int', nullable: true })
  riskAdjTaxArrears: number;

  // 其他字段
  @Column('json', { nullable: true })
  additionalFields: Record<string, any>;
}
