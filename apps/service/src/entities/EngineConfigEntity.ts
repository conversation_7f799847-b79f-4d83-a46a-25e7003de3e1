import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

export enum EngineStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ERROR = 'error',
}

@Entity('engine_configs')
@Index(['modelType'])
@Index(['status'])
export class EngineConfigEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  name: string;

  @Column({ name: 'model_type', length: 50 })
  modelType: string;

  @Column({ name: 'api_url', length: 500 })
  apiUrl: string;

  @Column({ name: 'api_key', length: 255 })
  apiKey: string;

  @Column('json', { nullable: true })
  parameters: Record<string, any>;

  @Column({
    type: 'enum',
    enum: EngineStatus,
    default: EngineStatus.ACTIVE,
  })
  status: EngineStatus;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
