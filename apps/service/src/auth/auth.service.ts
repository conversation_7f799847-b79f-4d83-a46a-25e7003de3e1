import { Injectable, UnauthorizedException } from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import { LoginDto } from './dto/login.dto';
import { randomBytes } from 'crypto';
import { RedisService } from './redis.service';
import { UserModel } from './dto/UserModel';

@Injectable()
export class AuthService {
  constructor(private readonly redisService: RedisService) {}

  private generateSessionId(): string {
    return randomBytes(32).toString('hex');
  }

  private getSessionKey(sessionId: string): string {
    return `auth:session:${sessionId}`;
  }

  async validateUser(username: string, password: string): Promise<UserModel | null> {
    // 简单的用户验证实现，优先保证系统可用性
    const mockUsers = [
      {
        id: 1,
        username: 'admin',
        password: await bcrypt.hash('admin123', 10),
        email: '<EMAIL>',
        roles: ['admin'],
        permissions: ['read', 'write', 'delete', 'manage_users', 'manage_models', 'manage_deployments'],
      },
      {
        id: 2,
        username: 'scientist',
        password: await bcrypt.hash('scientist123', 10),
        email: '<EMAIL>',
        roles: ['data_scientist'],
        permissions: ['read', 'write', 'train_models', 'evaluate_models'],
      },
      {
        id: 3,
        username: 'engineer',
        password: await bcrypt.hash('engineer123', 10),
        email: '<EMAIL>',
        roles: ['ml_engineer'],
        permissions: ['read', 'write', 'deploy_models', 'monitor_deployments'],
      },
      {
        id: 4,
        username: 'viewer',
        password: await bcrypt.hash('viewer123', 10),
        email: '<EMAIL>',
        roles: ['viewer'],
        permissions: ['read'],
      },
    ];

    const user = mockUsers.find((u) => u.username === username);
    if (user) {
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (isPasswordValid) {
        const { password, ...result } = user;
        return result;
      }
    }

    return null;
  }

  async login(loginDto: LoginDto) {
    const user: UserModel | null = await this.validateUser(loginDto.username, loginDto.password);
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const sessionId = this.generateSessionId();
    const sessionKey = this.getSessionKey(sessionId);

    // 存储用户信息到 Redis，设置30分钟过期（自动登出）
    await this.redisService.set(sessionKey, JSON.stringify(user), 30 * 60); // 30分钟

    return { user, sessionId };
  }

  async validateSession(sessionId: string): Promise<UserModel | null> {
    const sessionKey = this.getSessionKey(sessionId);
    const userData = await this.redisService.get(sessionKey);

    if (!userData) {
      return null;
    }

    return JSON.parse(userData);
  }

  async removeSession(sessionId: string) {
    const sessionKey = this.getSessionKey(sessionId);
    await this.redisService.del(sessionKey);
  }
}
