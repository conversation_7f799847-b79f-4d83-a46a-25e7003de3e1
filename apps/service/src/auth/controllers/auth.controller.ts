import { Controller, Post, UseGuards, Req, Body, Get, Res } from '@nestjs/common';
import { AuthService } from '../auth.service';
import { LoginDto } from '../dto/login.dto';
import { Request, Response } from 'express';
import { CustomAuthGuard } from '../guards/custom-auth.guard';
import { Public } from '../decorators/public.decorator';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Get('/debug-sentry')
  getError() {
    throw new Error('My first Sentry error!');
  }

  @Public()
  @Post('login')
  async login(@Body() loginDto: LoginDto, @Res({ passthrough: true }) response: Response) {
    const { user, sessionId } = await this.authService.login(loginDto);

    // 设置cookie
    response.cookie('MTSESSIONID', sessionId, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 30 * 60 * 1000, // 30分钟会话超时
    });

    return { user };
  }

  @Get('me')
  @UseGuards(CustomAuthGuard)
  async getCurrentUser(@Req() req: Request) {
    return { user: req['user'] };
  }

  @Get('test')
  @UseGuards(CustomAuthGuard)
  async testLogin(@Req() req: Request) {
    return req['user'];
  }

  @Post('logout')
  @UseGuards(CustomAuthGuard)
  async logout(@Req() req: Request, @Res({ passthrough: true }) response: Response) {
    const sessionId = req.cookies['MTSESSIONID'];
    if (sessionId) {
      await this.authService.removeSession(sessionId);
    }

    // 清除cookie
    response.clearCookie('MTSESSIONID');
    return { message: 'Logged out successfully' };
  }
}
