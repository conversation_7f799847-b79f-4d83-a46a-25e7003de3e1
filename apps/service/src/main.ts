import './sentry-instrument';

import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app/app.module';
import { NextFunction } from 'express';
import { join } from 'path';
import * as cookieParser from 'cookie-parser';

import { GlobalExceptionFilter } from './exceptions/GlobalExceptionFilter';
import { apiBasePrefix } from './common/constants';

async function bootstrap() {
  const app: NestExpressApplication = await NestFactory.create(AppModule, { logger: ['log', 'error', 'warn'], rawBody: true });

  // 添加 cookie-parser 中间件
  app.use(cookieParser());

  // global exceptions
  app.useGlobalFilters(new GlobalExceptionFilter());
  // 前端路由兜底
  app.use((req: Request, res: Response, next: NextFunction) => {
    //@ts-ignore
    if (!req.path.startsWith('/api') && !req.path.includes('.')) {
      //@ts-ignore
      res.sendFile(join(__dirname, '../../client/', 'dist', 'index.html'));
    } else {
      next();
    }
  });
  app.setGlobalPrefix(apiBasePrefix);
  app.useBodyParser('text', { limit: '50mb' });
  app.useBodyParser('json', { limit: '50mb' });

  // transform
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      // exceptionFactory: handClassValidatorError,
      whitelist: false,
    })
  );

  const options = new DocumentBuilder()
    .setTitle('Kzz Model Training Platform Service')
    .setDescription('Kzz Model Training Platform Service')
    .setVersion('1.0')
    .build();
  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup(`${apiBasePrefix}/swagger`, app, document);
  await app.listen(7001);
}
bootstrap();
