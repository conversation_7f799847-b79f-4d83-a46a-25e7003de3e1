import { Module, type ModuleMetadata } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_FILTER, APP_GUARD } from '@nestjs/core';
import { ServeStaticModule } from '@nestjs/serve-static';
import { TypeOrmModule, TypeOrmModuleOptions } from '@nestjs/typeorm';
import { join } from 'path';
import { AuthModule } from 'src/auth/auth.module';
import { CustomAuthGuard } from 'src/auth/guards/custom-auth.guard';
import configuration from 'src/config/configuration';
import { GlobalExceptionFilter } from 'src/exceptions/GlobalExceptionFilter';
import { SentryModule } from '@sentry/nestjs/setup';
import { CommonModule } from 'src/common/common.module';
import { DatasetsModule } from 'src/datasets/datasets.module';
import { TrainingModule } from 'src/training/training.module';

const getImportModules = () => {
  const modules: ModuleMetadata['imports'] = [
    SentryModule.forRoot(),
    ConfigModule.forRoot({
      load: [configuration],
      isGlobal: true,
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => configService.get<TypeOrmModuleOptions>('typeorm'),
    }),
    AuthModule,
    CommonModule,
    DatasetsModule,
    TrainingModule,
  ];
  //   if (process.env.NODE_ENV === 'production') {
  modules.push(
    ServeStaticModule.forRootAsync({
      useFactory: () => {
        const rootPath = join(__dirname, '../../../client/dist/');
        return [
          {
            rootPath,
            serveRoot: '/', // 显式指定服务根路径
            serveStaticOptions: {
              index: 'index.html',
              fallthrough: true,
              maxAge: '1d',
              //   setHeaders: (res, path, stat) => {
              //     // 对于 API 请求，不设置缓存头
              //     if (path.startsWith('/api')) {
              //       res.setHeader('Cache-Control', 'no-store');
              //     }
              //   }
            },
          },
        ];
      },
    })
  );
  //   }
  return modules;
};

@Module({
  imports: getImportModules(),
  controllers: [],
  providers: [
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },
    {
      provide: APP_GUARD,
      useClass: CustomAuthGuard,
    },
  ],
})
export class AppModule {}
