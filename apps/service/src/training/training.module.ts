import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TrainingJobEntity, ModelEntity, EngineConfigEntity } from '../entities';
import { TrainingService } from './training.service';
import { TrainingController } from './training.controller';
import { CommonModule } from '../common/common.module';
import { DatasetsModule } from '../datasets/datasets.module';

@Module({
  imports: [TypeOrmModule.forFeature([TrainingJobEntity, ModelEntity, EngineConfigEntity]), CommonModule, DatasetsModule],
  controllers: [TrainingController],
  providers: [TrainingService],
  exports: [TrainingService],
})
export class TrainingModule {}
