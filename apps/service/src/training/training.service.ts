import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JobStatus, ModelEntity, ModelStatus, TrainingJobEntity, EngineConfigEntity } from '../entities';
import { StorageService } from '../common/storage.service';
import { CreateTrainingJobDto } from './dto/create-training-job.dto';
import { UpdateTrainingJobDto } from './dto/update-training-job.dto';
import { TrainingJobFiltersDto } from './dto/training-job-filters.dto';
import { DatasetsService } from '../datasets/datasets.service';

@Injectable()
export class TrainingService {
  constructor(
    @InjectRepository(TrainingJobEntity)
    private trainingJobRepository: Repository<TrainingJobEntity>,
    @InjectRepository(ModelEntity)
    private modelRepository: Repository<ModelEntity>,
    @InjectRepository(EngineConfigEntity)
    private engineConfigRepository: Repository<EngineConfigEntity>,
    private storageService: StorageService,
    private datasetsService: DatasetsService
  ) {}

  /**
   * 创建训练任务
   */
  async create(createTrainingJobDto: CreateTrainingJobDto, userId: string): Promise<TrainingJobEntity> {
    // 验证数据集是否存在
    const dataset = await this.datasetsService.findOne(createTrainingJobDto.datasetId);

    // 验证数据集类型是否匹配
    if (dataset.modelType !== createTrainingJobDto.modelType) {
      throw new BadRequestException(`数据集类型 ${dataset.modelType} 与训练任务类型 ${createTrainingJobDto.modelType} 不匹配`);
    }

    // 验证训练引擎是否存在
    const engine = await this.engineConfigRepository.findOne({ where: { id: createTrainingJobDto.engineId } });
    if (!engine) {
      throw new BadRequestException(`训练引擎 ${createTrainingJobDto.engineId} 不存在`);
    }

    // 验证训练引擎类型是否匹配
    if (engine.modelType !== createTrainingJobDto.modelType) {
      throw new BadRequestException(`训练引擎类型 ${engine.modelType} 与训练任务类型 ${createTrainingJobDto.modelType} 不匹配`);
    }

    // 创建训练任务
    const trainingJob = this.trainingJobRepository.create({
      ...createTrainingJobDto,
      createdBy: userId,
      status: JobStatus.PENDING,
    });

    return this.trainingJobRepository.save(trainingJob);
  }

  /**
   * 查找所有训练任务
   */
  async findAll(filters: TrainingJobFiltersDto): Promise<[TrainingJobEntity[], number]> {
    const query = this.trainingJobRepository.createQueryBuilder('job');

    if (filters.modelType) {
      query.andWhere('job.modelType = :modelType', { modelType: filters.modelType });
    }

    if (filters.status) {
      query.andWhere('job.status = :status', { status: filters.status });
    }

    if (filters.datasetId) {
      query.andWhere('job.datasetId = :datasetId', { datasetId: filters.datasetId });
    }

    if (filters.createdBy) {
      query.andWhere('job.createdBy = :createdBy', { createdBy: filters.createdBy });
    }

    if (filters.search) {
      query.andWhere('(job.name LIKE :search OR job.description LIKE :search)', {
        search: `%${filters.search}%`,
      });
    }

    query.orderBy('job.createdAt', filters.sortOrder || 'DESC');

    if (filters.skip !== undefined) {
      query.skip(filters.skip);
    }

    if (filters.take !== undefined) {
      query.take(filters.take);
    }

    return query.getManyAndCount();
  }

  /**
   * 查找单个训练任务
   */
  async findOne(id: string): Promise<TrainingJobEntity> {
    const trainingJob = await this.trainingJobRepository.findOne({ where: { id } });
    if (!trainingJob) {
      throw new NotFoundException(`训练任务 ${id} 不存在`);
    }
    return trainingJob;
  }

  /**
   * 更新训练任务
   */
  async update(id: string, updateTrainingJobDto: UpdateTrainingJobDto): Promise<TrainingJobEntity> {
    const trainingJob = await this.findOne(id);

    // 更新字段
    if (updateTrainingJobDto.name) trainingJob.name = updateTrainingJobDto.name;
    if (updateTrainingJobDto.description) trainingJob.description = updateTrainingJobDto.description;
    if (updateTrainingJobDto.status) trainingJob.status = updateTrainingJobDto.status;
    if (updateTrainingJobDto.parameters) trainingJob.parameters = updateTrainingJobDto.parameters;
    if (updateTrainingJobDto.metrics) trainingJob.metrics = updateTrainingJobDto.metrics;
    if (updateTrainingJobDto.logs) trainingJob.logs = updateTrainingJobDto.logs;

    // 更新状态相关字段
    if (updateTrainingJobDto.status === JobStatus.RUNNING && !trainingJob.startedAt) {
      trainingJob.startedAt = new Date();
    } else if (updateTrainingJobDto.status === JobStatus.COMPLETED && !trainingJob.completedAt) {
      trainingJob.completedAt = new Date();

      // 如果任务完成，创建模型
      if (!trainingJob.modelId) {
        const model = await this.createModel(trainingJob);
        trainingJob.modelId = model.id;
      }
    }

    return this.trainingJobRepository.save(trainingJob);
  }

  /**
   * 删除训练任务
   */
  async remove(id: string): Promise<void> {
    const trainingJob = await this.findOne(id);

    // 如果任务正在运行，不允许删除
    if (trainingJob.status === JobStatus.RUNNING) {
      throw new BadRequestException('无法删除正在运行的训练任务');
    }

    // 如果任务已完成并创建了模型，不删除模型

    await this.trainingJobRepository.remove(trainingJob);
  }

  /**
   * 启动训练任务
   */
  async startTraining(id: string): Promise<TrainingJobEntity> {
    const trainingJob = await this.findOne(id);

    // 检查任务状态
    if (trainingJob.status !== JobStatus.PENDING) {
      throw new BadRequestException(`无法启动状态为 ${trainingJob.status} 的训练任务`);
    }

    // 获取训练引擎配置
    const engine = await this.engineConfigRepository.findOne({ where: { id: trainingJob.engineId } });
    if (!engine) {
      throw new BadRequestException(`训练引擎 ${trainingJob.engineId} 不存在`);
    }

    // 更新任务状态
    trainingJob.status = JobStatus.RUNNING;
    trainingJob.startedAt = new Date();

    // 在实际应用中，这里会调用训练引擎API启动训练
    // 这里只是模拟训练过程
    this.simulateTraining(trainingJob.id, engine);

    return this.trainingJobRepository.save(trainingJob);
  }

  /**
   * 取消训练任务
   */
  async cancelTraining(id: string): Promise<TrainingJobEntity> {
    const trainingJob = await this.findOne(id);

    // 检查任务状态
    if (trainingJob.status !== JobStatus.RUNNING && trainingJob.status !== JobStatus.PENDING) {
      throw new BadRequestException(`无法取消状态为 ${trainingJob.status} 的训练任务`);
    }

    // 获取训练引擎配置
    const engine = await this.engineConfigRepository.findOne({ where: { id: trainingJob.engineId } });
    if (!engine) {
      throw new BadRequestException(`训练引擎 ${trainingJob.engineId} 不存在`);
    }

    // 更新任务状态
    trainingJob.status = JobStatus.CANCELLED;
    trainingJob.completedAt = new Date();

    // 在实际应用中，这里会调用训练引擎API取消训练

    return this.trainingJobRepository.save(trainingJob);
  }

  /**
   * 获取训练任务日志
   */
  async getLogs(id: string): Promise<string[]> {
    const trainingJob = await this.findOne(id);
    return trainingJob.logs || [];
  }

  /**
   * 添加训练日志
   */
  async addLog(id: string, log: string): Promise<void> {
    const trainingJob = await this.findOne(id);

    if (!trainingJob.logs) {
      trainingJob.logs = [];
    }

    trainingJob.logs.push(log);

    await this.trainingJobRepository.save(trainingJob);
  }

  /**
   * 获取训练引擎配置
   */
  async getEngineConfig(modelType: string): Promise<EngineConfigEntity[]> {
    return this.engineConfigRepository.find({ where: { modelType } });
  }

  /**
   * 创建模型
   */
  private async createModel(trainingJob: TrainingJobEntity): Promise<ModelEntity> {
    // 获取数据集信息
    const dataset = await this.datasetsService.findOne(trainingJob.datasetId);

    // 创建模型
    const model = this.modelRepository.create({
      name: `${trainingJob.name}_model`,
      description: `由训练任务 ${trainingJob.name} 生成的模型`,
      modelType: trainingJob.modelType,
      version: '1.0.0', // 实际应用中应该有版本管理逻辑
      trainingJobId: trainingJob.id,
      datasetId: trainingJob.datasetId,
      metrics: trainingJob.metrics,
      parameters: trainingJob.parameters,
      filePath: `models/${trainingJob.modelType}/${trainingJob.id}/model.zip`,
      createdBy: trainingJob.createdBy,
      status: ModelStatus.READY,
    });

    return this.modelRepository.save(model);
  }

  /**
   * 模拟训练过程
   * 注意：这只是一个模拟，实际应用中应该调用真实的训练引擎
   */
  private async simulateTraining(jobId: string, engine: EngineConfigEntity): Promise<void> {
    // 模拟训练过程
    const simulateTrainingProcess = async () => {
      try {
        // 添加初始日志
        await this.addLog(jobId, '训练开始');
        await this.addLog(jobId, `使用训练引擎: ${engine.name}`);

        // 模拟训练进度
        for (let i = 1; i <= 10; i++) {
          // 等待一段时间
          await new Promise((resolve) => setTimeout(resolve, 1000));

          // 添加进度日志
          await this.addLog(jobId, `训练进度: ${i * 10}%`);

          // 更新指标
          const metrics = {
            accuracy: 0.5 + i * 0.05,
            loss: 0.5 - i * 0.04,
            precision: 0.6 + i * 0.03,
            recall: 0.55 + i * 0.04,
          };

          await this.update(jobId, { metrics });
        }

        // 完成训练
        await this.addLog(jobId, '训练完成');
        await this.update(jobId, { status: JobStatus.COMPLETED });
      } catch (error) {
        // 训练失败
        await this.addLog(jobId, `训练失败: ${error.message}`);
        await this.update(jobId, { status: JobStatus.FAILED });
      }
    };

    // 异步执行模拟训练
    simulateTrainingProcess();
  }
}
