import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsObject, IsOptional, IsString, IsUUID, MaxLength } from 'class-validator';
import { ModelType } from '../../datasets/dto/create-dataset.dto';

export class CreateTrainingJobDto {
  @ApiProperty({ description: '训练任务名称' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({ description: '训练任务描述', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: '数据集ID' })
  @IsUUID()
  @IsNotEmpty()
  datasetId: string;

  @ApiProperty({
    description: '模型类型',
    enum: ModelType,
    example: ModelType.HEALTH_MODEL,
  })
  @IsEnum(ModelType)
  @IsNotEmpty()
  modelType: ModelType;

  @ApiProperty({ description: '训练引擎ID' })
  @IsUUID()
  @IsNotEmpty()
  engineId: string;

  @ApiProperty({
    description: '训练参数',
    type: 'object',
    example: {
      epochs: 100,
      batchSize: 32,
      learningRate: 0.001,
      optimizer: 'adam',
    },
  })
  @IsObject()
  @IsNotEmpty()
  parameters: Record<string, any>;
}
