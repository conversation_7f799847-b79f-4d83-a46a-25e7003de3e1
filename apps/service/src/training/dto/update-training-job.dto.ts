import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsObject, IsOptional, IsString, MaxLength } from 'class-validator';
import { JobStatus } from '../../entities/TrainingJobEntity';

export class UpdateTrainingJobDto {
  @ApiProperty({ description: '训练任务名称', required: false })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  name?: string;

  @ApiProperty({ description: '训练任务描述', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: '训练任务状态',
    enum: JobStatus,
    required: false,
  })
  @IsEnum(JobStatus)
  @IsOptional()
  status?: JobStatus;

  @ApiProperty({
    description: '训练参数',
    type: 'object',
    required: false,
    example: {
      epochs: 100,
      batchSize: 32,
      learningRate: 0.001,
      optimizer: 'adam',
    },
  })
  @IsObject()
  @IsOptional()
  parameters?: Record<string, any>;

  @ApiProperty({
    description: '训练指标',
    type: 'object',
    required: false,
    example: {
      accuracy: 0.95,
      precision: 0.92,
      recall: 0.91,
      f1Score: 0.915,
    },
  })
  @IsObject()
  @IsOptional()
  metrics?: Record<string, number>;

  @ApiProperty({
    description: '训练日志',
    type: [String],
    required: false,
  })
  @IsOptional()
  logs?: string[];
}
