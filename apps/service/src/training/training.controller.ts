import { Controller, Get, Post, Body, Patch, Param, Delete, Query, Req } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { TrainingService } from './training.service';
import { CreateTrainingJobDto } from './dto/create-training-job.dto';
import { UpdateTrainingJobDto } from './dto/update-training-job.dto';
import { TrainingJobFiltersDto } from './dto/training-job-filters.dto';
import { Request } from 'express';
import { UserModel } from '../auth/dto/UserModel';
import { Roles } from '../auth/decorators/roles.decorator';

@ApiTags('training')
@Controller('training')
export class TrainingController {
  constructor(private readonly trainingService: TrainingService) {}

  @Post()
  @ApiOperation({ summary: '创建训练任务' })
  @Roles('admin', 'data_scientist')
  async create(@Body() createTrainingJobDto: CreateTrainingJobDto, @Req() req: Request) {
    const user = req['user'] as UserModel;
    return this.trainingService.create(createTrainingJobDto, user.id.toString());
  }

  @Get()
  @ApiOperation({ summary: '获取训练任务列表' })
  async findAll(@Query() filters: TrainingJobFiltersDto) {
    const [jobs, total] = await this.trainingService.findAll(filters);
    return {
      data: jobs,
      total,
      page: filters.skip ? Math.floor(filters.skip / (filters.take || 10)) + 1 : 1,
      pageSize: filters.take || 10,
    };
  }

  @Get(':id')
  @ApiOperation({ summary: '获取训练任务详情' })
  async findOne(@Param('id') id: string) {
    return this.trainingService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新训练任务' })
  @Roles('admin', 'data_scientist')
  async update(@Param('id') id: string, @Body() updateTrainingJobDto: UpdateTrainingJobDto) {
    return this.trainingService.update(id, updateTrainingJobDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除训练任务' })
  @Roles('admin', 'data_scientist')
  async remove(@Param('id') id: string) {
    await this.trainingService.remove(id);
    return { success: true };
  }

  @Post(':id/start')
  @ApiOperation({ summary: '启动训练任务' })
  @Roles('admin', 'data_scientist')
  async startTraining(@Param('id') id: string) {
    return this.trainingService.startTraining(id);
  }

  @Post(':id/cancel')
  @ApiOperation({ summary: '取消训练任务' })
  @Roles('admin', 'data_scientist')
  async cancelTraining(@Param('id') id: string) {
    return this.trainingService.cancelTraining(id);
  }

  @Get(':id/logs')
  @ApiOperation({ summary: '获取训练任务日志' })
  async getLogs(@Param('id') id: string) {
    const logs = await this.trainingService.getLogs(id);
    return { logs };
  }

  @Get('engines/:modelType')
  @ApiOperation({ summary: '获取训练引擎配置' })
  async getEngineConfig(@Param('modelType') modelType: string) {
    return this.trainingService.getEngineConfig(modelType);
  }
}
